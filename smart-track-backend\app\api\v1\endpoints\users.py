from fastapi import APIRouter, HTTPException, status, Query, Depends
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional

from app.core.database import get_db
from app.core.security import get_current_user, get_password_hash
from app.models.user import User, Role, UserProfile
from app.models.customer import DistributionChannel
from app.models.customer import Branch
from app.schemas.user import (
    UserResponse, 
    UserDetailResponse, 
    UserCreate, 
    UserUpdate,
    RoleResponse,
    UserProfileResponse
)
from app.schemas.auth import UserResponse as AuthUserResponse

router = APIRouter()

# =============================================================================
# IMPORTANT: All specific routes MUST be defined BEFORE the generic /{user_id} route
# to avoid route conflicts. FastAPI matches routes in order of definition.
# =============================================================================

# Test endpoints - these must come first
@router.get("/test-endpoint")
async def test_endpoint():
    """Simple test endpoint - force refresh v2"""
    return {"message": "Users router is working", "endpoint": "test-endpoint"}

@router.get("/debug-simple")
async def debug_simple():
    """Debug endpoint to test basic functionality"""
    print("DEBUG: debug-simple endpoint hit!")
    return {"message": "Debug endpoint working", "status": "ok"}

# =============================================================================
# Other specific routes - these must also come before /{user_id}
# =============================================================================

@router.get("/roles", response_model=List[RoleResponse])
async def get_roles(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all available roles"""
    roles = db.query(Role).filter(Role.is_active == True).all()
    return roles

@router.get("/by-distribution-channel/{channel_id}", response_model=List[UserDetailResponse])
async def get_users_by_distribution_channel(
    channel_id: int,
    role_type: Optional[str] = Query(None, description="Filter by role type"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get users assigned to a specific distribution channel"""
    # Verify channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    query = db.query(User).join(UserProfile).filter(
        UserProfile.distribution_channel_id == channel_id,
        User.is_active == True
    )
    
    if role_type:
        query = query.filter(UserProfile.role_type == role_type)
    
    users = query.all()
    return users

# =============================================================================
# Main CRUD endpoints - the /{user_id} pattern must come AFTER all specific routes
# =============================================================================

@router.get("/", response_model=List[UserDetailResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    role_id: Optional[int] = Query(None, description="Filter by role"),
    distribution_channel_id: Optional[int] = Query(None, description="Filter by distribution channel"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of users with filtering options"""
    # TODO: Add role-based access control
    query = db.query(User)
    
    if role_id:
        query = query.filter(User.role_id == role_id)
    
    if is_active is not None:
        query = query.filter(User.is_active == is_active)
    
    if distribution_channel_id:
        query = query.join(UserProfile).filter(
            UserProfile.distribution_channel_id == distribution_channel_id
        )
    
    # Order by creation date descending to show newest users first
    users = query.order_by(User.created_at.desc()).offset(skip).limit(limit).all()
    return users

@router.post("/", response_model=UserDetailResponse)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new user - requires admin role"""
    # Check if current user has admin permissions (role-based access control)
    if not current_user.role or current_user.role.name.lower() not in ['admin', 'administrator']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create users"
        )
    
    # Check if username or email already exists
    existing = db.query(User).filter(
        (User.username == user_data.username) | (User.email == user_data.email)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already exists"
        )
    
    # Check if SAP code already exists
    existing_sap = db.query(User).filter(User.sap_code == user_data.sap_code).first()
    if existing_sap:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="SAP code already exists"
        )
    
    # Validate role exists
    role = db.query(Role).filter(Role.id == user_data.role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Create user
    new_user = User(
        sap_code=user_data.sap_code,
        username=user_data.username,
        email=user_data.email,
        full_name=user_data.full_name,
        phone=user_data.phone,
        hashed_password=get_password_hash(user_data.password),
        is_active=user_data.is_active,
        is_verified=user_data.is_verified,
        role_id=user_data.role_id
    )
    
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return new_user

# =============================================================================
# Generic routes with path parameters - MUST be at the very end
# =============================================================================

@router.get("/{user_id}", response_model=UserDetailResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user by ID with full details"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.put("/{user_id}", response_model=UserDetailResponse)
async def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):

    """Update user information - admin can update any user, users can update themselves"""
    # Check permissions: admin can update anyone, users can only update themselves
    if current_user.id != user_id:
        if not current_user.role or current_user.role.name.lower() not in ['admin', 'administrator']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only update your own profile or you need admin privileges"
            )
    
    # Check if username or email already exists for other users
    update_data = user_update.dict(exclude_unset=True)
    
    if 'username' in update_data and update_data['username'] != user.username:
        existing = db.query(User).filter(
            User.username == update_data['username'],
            User.id != user_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
    
    if 'email' in update_data and update_data['email'] != user.email:
        existing = db.query(User).filter(
            User.email == update_data['email'],
            User.id != user_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
    
    if 'sap_code' in update_data and update_data['sap_code'] != user.sap_code:
        existing = db.query(User).filter(
            User.sap_code == update_data['sap_code'],
            User.id != user_id
        ).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="SAP code already exists"
            )
    
    # Validate role exists if role_id is being updated
    if 'role_id' in update_data:
        role = db.query(Role).filter(Role.id == update_data['role_id']).first()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
    
    # Update user fields
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    
    return user

@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete user (soft delete by setting is_active to False)"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Soft delete - set is_active to False
    user.is_active = False
    db.commit()
    
    return {"message": "User deleted successfully"}

@router.get("/{user_id}/profile", response_model=UserProfileResponse)
async def get_user_profile(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user profile information"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    return profile

@router.get("/by-distribution-channel/{channel_id}", response_model=List[UserDetailResponse])
async def get_users_by_distribution_channel(
    channel_id: int,
    role_type: Optional[str] = Query(None, description="Filter by role type"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get users assigned to a specific distribution channel"""
    # Verify channel exists
    channel = db.query(DistributionChannel).filter(
        DistributionChannel.id == channel_id
    ).first()
    
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Distribution channel not found"
        )
    
    query = db.query(User).join(UserProfile).filter(
        UserProfile.distribution_channel_id == channel_id,
        User.is_active == True
    )
    
    if role_type:
        query = query.filter(UserProfile.role_type == role_type)
    
    users = query.all()
    return users

@router.get("/me", response_model=UserDetailResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's profile information"""
    return current_user
